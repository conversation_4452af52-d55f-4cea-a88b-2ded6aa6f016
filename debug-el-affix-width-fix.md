# el-affix 宽度问题修复说明

## 问题描述
在选择工程类项目时，左侧步骤条的 `el-affix` 组件的 `width` 会被设为 `0px`，导致步骤条消失或显示异常。

## 问题原因
1. **el-affix 固定定位机制**：当 `el-affix` 激活固定定位时，它会将原始元素从文档流中移除，并创建一个固定定位的副本
2. **宽度计算问题**：在某些情况下（如内容动态变化），`el-affix` 可能无法正确计算或保持原始宽度
3. **响应式更新**：当选择不同类型的项目时，页面内容发生变化，可能触发 `el-affix` 重新计算，导致宽度丢失

## 解决方案

### 1. CSS 强制宽度设置
```scss
.steps-sidebar {
  width: 190px;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px 10px;
  border: none;
  min-height: 400px;
  box-sizing: border-box; // 确保盒模型正确
}

/* 修复 el-affix 宽度问题 */
.el-affix {
  width: 190px !important;
}

.el-affix--fixed {
  width: 190px !important;
}
```

### 2. JavaScript 动态修复
```javascript
// 处理 el-affix 状态变化
const handleAffixChange = (fixed) => {
  console.log('el-affix 状态变化:', fixed);
  nextTick(() => {
    const affixElements = document.querySelectorAll('.el-affix');
    affixElements.forEach(el => {
      if (el && el.style) {
        el.style.width = '190px';
      }
    });
  });
};

const calculateAffixOffset = () => {
  // 原有的偏移量计算逻辑
  const viewportHeight = window.innerHeight;
  const sidebarHeight = 400;
  affixOffset.value = Math.max(0, (viewportHeight - sidebarHeight) / 2);
  
  // 修复 el-affix 宽度问题
  nextTick(() => {
    const affixElements = document.querySelectorAll('.el-affix');
    affixElements.forEach(el => {
      if (el && el.style) {
        el.style.width = '190px';
      }
    });
  });
};
```

### 3. 模板事件监听
```vue
<el-affix :offset="affixOffset" :z-index="100" @change="handleAffixChange">
  <div class="steps-sidebar">
    <!-- 步骤内容 -->
  </div>
</el-affix>
```

### 4. 组件挂载时的处理
```javascript
onMounted(() => {
  calculateAffixOffset();
  // 监听窗口大小变化，重新计算偏移量
  window.addEventListener('resize', calculateAffixOffset);
  
  // 延迟执行，确保 DOM 完全渲染
  setTimeout(() => {
    calculateAffixOffset();
  }, 100);
});
```

## 修复策略

### 多层保障机制
1. **CSS 强制设置**：使用 `!important` 确保宽度不被覆盖
2. **事件监听**：监听 `el-affix` 的 `@change` 事件，在状态变化时修复宽度
3. **定时修复**：在关键时机（组件挂载、窗口调整）主动修复宽度
4. **DOM 查询修复**：通过 `querySelector` 直接设置样式

### 触发时机
- 组件挂载时
- `el-affix` 状态变化时（固定/取消固定）
- 窗口大小调整时
- 内容动态变化时

## 技术细节

### el-affix 工作原理
1. 监听滚动事件
2. 当元素到达指定位置时，将其设为 `position: fixed`
3. 在原位置创建一个占位元素
4. 可能在此过程中丢失原始宽度信息

### 宽度丢失的常见场景
- 父容器使用 Flexbox 布局
- 内容动态变化导致重新计算
- 响应式布局调整
- 组件重新渲染

## 验证步骤
1. 打开项目助手页面
2. 选择不同类型的项目（特别是工程类项目）
3. 滚动页面，观察左侧步骤条是否正常显示
4. 检查开发者工具中 `.el-affix` 元素的宽度是否为 `190px`
5. 调整窗口大小，确认步骤条宽度保持正常

## 调试信息
- `console.log('el-affix 状态变化:', fixed);` - 监听状态变化
- 开发者工具检查 `.el-affix` 和 `.el-affix--fixed` 的样式
- 观察 `width` 属性是否被正确设置

## 注意事项
1. 使用 `!important` 可能影响其他样式，需要谨慎使用
2. `nextTick` 确保在 DOM 更新后执行修复逻辑
3. 事件监听器需要在组件销毁时清理
4. 响应式设计中可能需要不同的宽度值

## 兼容性
- 支持 Element Plus 的 `el-affix` 组件
- 兼容不同屏幕尺寸
- 适用于动态内容变化的场景
