<template>
  <div class="step-content">
    <!-- <h3>选择项目</h3> -->
    
    <div class="form-item">
      <label>项目名称</label>
      <el-select v-model="formData.selectedProject" placeholder="请选择项目" class="full-width" clearable filterable @change="handleProjectChange">
        <el-option
          v-for="item in projectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>

    <!-- 项目信息标签显示 -->
    <div v-if="formData.selectedProject" class="project-info-tags">
      <div class="tag-row">
        <div class="tag-item">
          <span class="tag-label">项目编号：</span>
          <el-tag type="info" size="default">{{ formData.projectCode || '未设置' }}</el-tag>
        </div>
        <div class="tag-item">
          <span class="tag-label">采购方式：</span>
          <el-tag type="success" size="default">{{ formData.procurementMethod || '未设置' }}</el-tag>
        </div>
      </div>

      <div class="tag-row">
        <div class="tag-item">
          <span class="tag-label">项目类型：</span>
          <el-tag type="warning" size="default">{{ formData.projectType || '未设置' }}</el-tag>
        </div>
        <div class="tag-item">
          <span class="tag-label">算法类型：</span>
          <el-tag type="danger" size="default">{{ formData.algorithmCategory || '未设置' }}</el-tag>
        </div>
      </div>

      <div class="tag-row" v-if="formData.projectDescription">
        <div class="tag-item full-width">
          <span class="tag-label">项目描述：</span>
          <div class="description-content">{{ formData.projectDescription }}</div>
        </div>
      </div>
    </div>

    <div class="form-item" :class="{ 'highlight-table': shouldHighlightTable }">
      <label>标准表</label>
      <!-- 高亮提示信息 -->
      <transition name="fade-slide" mode="out-in">
        <div v-if="shouldHighlightTable" class="highlight-tip">
          <el-alert
            title="请填写标准表信息"
            description="项目已选择，请在下方标准表中填写相关信息"
            type="info"
            :closable="false"
            show-icon
            class="highlight-alert">
          </el-alert>
        </div>
      </transition>
            <div class="standard-table-container" :class="{ 'table-highlighted': shouldHighlightTable }">
              <!-- 根据算法类型动态显示不同的标准表 -->
              <div class="table-selector">
                <!-- 当算法类型为工程咨询类时显示工程咨询类标准表 -->
                <div v-if="formData.algorithmCategory === '工程咨询类'">
                  <h4 class="table-title">工程咨询类标准表</h4>
                  <div class="engineering-table">
                    <el-table :data="engineeringTableRows" border style="width: 100%" :header-cell-style="{background:'#f5f7fa',color:'#606266'}" table-layout="auto">
                      <el-table-column label="工程咨询类别" min-width="200" align="center" show-overflow-tooltip>
                        <template #default="scope">
                          <el-cascader
                            v-model="scope.row.category"
                            :options="engineeringCategoryOptions"
                            placeholder="请选择工程咨询类别"
                            clearable
                            :show-all-levels="false"
                            style="width: 100%"
                            @change="() => handleEngineeringCategoryChange(scope.row)"
                          ></el-cascader>
                        </template>
                      </el-table-column>
                      <el-table-column label="建筑安装工程造价(万元)" min-width="120" align="center" v-if="shouldShowColumn('constructionCost')">
                        <template #default="scope">
                          <el-input v-model="scope.row.constructionCost" type="number" placeholder="请输入" v-if="getFieldVisibility(getConsultingType(scope.row)).constructionCost"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column label="建设项目总投资(万元)" min-width="120" align="center" v-if="shouldShowColumn('totalInvestment')">
                        <template #default="scope">
                          <el-input v-model="scope.row.totalInvestment" type="number" placeholder="请输入" v-if="getFieldVisibility(getConsultingType(scope.row)).totalInvestment"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column label="土地及人民政府规费(万元)" min-width="140" align="center" v-if="shouldShowColumn('landAndGovFees')">
                        <template #default="scope">
                          <el-input v-model="scope.row.landAndGovFees" type="number" placeholder="请输入" v-if="getFieldVisibility(getConsultingType(scope.row)).landAndGovFees"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column label="评估值(万元)" min-width="100" align="center" v-if="shouldShowColumn('evaluationValue')">
                        <template #default="scope">
                          <el-input v-model="scope.row.evaluationValue" type="number" placeholder="请输入" v-if="getFieldVisibility(getConsultingType(scope.row)).evaluationValue"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column label="计费额（万元）" min-width="360" align="center" v-if="shouldShowColumn('billingAmount')">
                        <el-table-column label="建筑安装工程费" min-width="120" align="center">
                          <template #default="scope">
                            <el-input v-model="scope.row.billingAmount.constructionFee" type="number" placeholder="请输入" v-if="getFieldVisibility(getConsultingType(scope.row)).billingAmount"></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="设备购置费" min-width="120" align="center">
                          <template #default="scope">
                            <el-input v-model="scope.row.billingAmount.equipmentFee" type="number" placeholder="请输入" v-if="getFieldVisibility(getConsultingType(scope.row)).billingAmount"></el-input>
                          </template>
                        </el-table-column>
                        <el-table-column label="联合试运转费" min-width="120" align="center">
                          <template #default="scope">
                            <el-input v-model="scope.row.billingAmount.testRunFee" type="number" placeholder="请输入" v-if="getFieldVisibility(getConsultingType(scope.row)).billingAmount"></el-input>
                          </template>
                        </el-table-column>
                      </el-table-column>
                      <el-table-column label="专业调整系数" min-width="120" align="center" v-if="shouldShowColumn('professionalAdjustment')">
                        <template #header>
                          <span>专业调整系数</span>
                          <el-tooltip content="0.8（园林绿化工程）; 1.0（建筑、人防、市政公用工程）;1.0(邮政、电信、广播电视工程)" placement="top">
                            <el-icon><QuestionFilled /></el-icon>
                          </el-tooltip>
                        </template>
                        <template #default="scope">
                          <el-select v-model="scope.row.professionalAdjustment" placeholder="请选择" style="width: 100%" v-if="getFieldVisibility(getConsultingType(scope.row)).professionalAdjustment">
                            <el-option v-for="item in professionalAdjustmentOptions" :key="item" :label="item" :value="item"></el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column label="工程复杂程度调整系数" min-width="160" align="center" v-if="shouldShowColumn('complexityAdjustment')">
                        <template #default="scope">
                          <el-select v-model="scope.row.complexityAdjustment" placeholder="请选择" style="width: 100%" v-if="getFieldVisibility(getConsultingType(scope.row)).complexityAdjustment">
                            <el-option v-for="item in complexityAdjustmentOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column label="高程调整系数" min-width="220" align="center" v-if="shouldShowColumn('altitudeAdjustment')">
                        <template #default="scope">
                          <el-select v-model="scope.row.altitudeAdjustment" placeholder="请选择" style="width: 100%" v-if="getFieldVisibility(getConsultingType(scope.row)).altitudeAdjustment">
                            <el-option v-for="item in altitudeAdjustmentOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                          </el-select>
                        </template>
                      </el-table-column>
                      <el-table-column label="附加调整系数" min-width="120" align="center" v-if="shouldShowColumn('additionalAdjustment')">
                        <template #default="scope">
                          <el-input v-model="scope.row.additionalAdjustment" type="number" placeholder="请输入" v-if="getFieldVisibility(getConsultingType(scope.row)).additionalAdjustment"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column label="其他设计收费(万元)" min-width="120" align="center" v-if="shouldShowColumn('otherDesignFees')">
                        <template #default="scope">
                          <el-input v-model="scope.row.otherDesignFees" type="number" placeholder="请输入" v-if="getFieldVisibility(getConsultingType(scope.row)).otherDesignFees"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="80" align="center" fixed="right">
                        <template #default="scope">
                          <el-button 
                            type="danger" 
                            icon="Delete" 
                            circle 
                            size="small" 
                            @click="removeEngineeringTableRow(scope.$index)"
                            v-if="engineeringTableRows.length > 1">
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <div class="add-row">
                      <el-button
                        type="primary"
                        icon="Plus"
                        circle
                        size="default"
                        @click="addEngineeringTableRow"
                        class="add-row-btn">
                      </el-button>
                    </div>
                  </div>
                </div>
                
                <!-- 当算法类型为培训类时显示培训类标准表 -->
                <div v-else-if="formData.algorithmCategory === '培训类'">
                  <h4 class="table-title">培训类标准表</h4>
                  <div class="standard-table">
                    <!-- 培训类标准表表头 -->
                    <div class="table-header">
                      <div class="header-item">培训类型</div>
                      <div class="header-item">培训天数</div>
                      <div class="header-item">学员数量</div>
                      <div class="header-item">每天学时</div>
                      <div class="header-item">师资职称</div>
                      <div class="header-item">培训类别</div>
                      <div class="header-item">操作</div>
                    </div>
                    
                    <!-- 培训类表格行 -->
                    <div v-for="(row, index) in trainingTableRows" :key="index" class="table-row">
                      <div class="row-item">
                        <el-select
                          v-model="row.training_type"
                          placeholder="请选择培训类型"
                          class="full-width">
                          <el-option label="线上培训" value="线上培训"></el-option>
                          <el-option label="线下培训" value="线下培训"></el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-input-number v-model="row.training_days" controls-position="right" :min="1" placeholder="培训天数"></el-input-number>
                      </div>
                      <div class="row-item">
                        <el-input-number v-model="row.trainee_count" controls-position="right" :min="1" placeholder="学员数量"></el-input-number>
                      </div>
                      <div class="row-item">
                        <el-input-number v-model="row.daily_hours" controls-position="right" :min="1" :max="24" placeholder="每天学时"></el-input-number>
                      </div>
                      <div class="row-item">
                        <el-select
                          v-model="row.instructor_title"
                          placeholder="讲师职称"
                          class="full-width">
                          <el-option
                            v-for="item in trainingCategories.teacherTitles"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-select
                          v-model="row.training_category"
                          placeholder="培训类别"
                          class="full-width">
                          <el-option label="省部级及以上（一类）" value="省部级及以上（一类）"></el-option>
                          <el-option label="司局级（二类）" value="司局级（二类）"></el-option>
                          <el-option label="处级及以下（三类）" value="处级及以下（三类）"></el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-button 
                          type="danger" 
                          icon="Delete" 
                          circle 
                          size="small" 
                          @click="removeTrainingTableRow(index)"
                          v-if="trainingTableRows.length > 1">
                        </el-button>
                      </div>
                    </div>
                    
                    <div class="add-row">
                      <el-button
                        type="primary"
                        icon="Plus"
                        circle
                        size="default"
                        @click="addTrainingTableRow"
                        class="add-row-btn">
                      </el-button>
                    </div>
                  </div>
                </div>

                <!-- 当算法类型为货物类、劳务类、信息类、办公类、综合类时显示京东慧采标准表 -->
                <div v-else>
                  <h4 class="table-title">京东慧采标准表</h4>
                  <div class="standard-table">
                    <div class="table-header">
                      <div class="header-item">费用名称</div>
                      <div class="header-item">一级分类</div>
                      <div class="header-item">二级分类</div>
                      <div class="header-item">数量</div>
                      <div class="header-item">单位</div> <!-- 新增单位列 -->
                      <div class="header-item">商品属性</div>
                      <div class="header-item">操作</div>
                    </div>
                    
                    <!-- 使用v-for循环渲染多行 -->
                    <div v-for="(row, index) in jdTableRows" :key="index" class="table-row">
                      <div class="row-item">
                        <el-select 
                          v-model="row.feeName" 
                          placeholder="请选择费用名称" 
                          class="full-width"
                          @change="handleFeeNameChange(row)">
                          <el-option 
                            v-for="item in jdCategories.feeNames" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-select 
                          v-model="row.firstCategory" 
                          placeholder="一级分类" 
                          class="full-width">
                          <el-option 
                            v-for="item in jdCategories.firstCategories" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-select 
                          v-model="row.secondCategory" 
                          placeholder="二级分类" 
                          class="full-width">
                          <el-option 
                            v-for="item in jdCategories.secondCategories" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <!-- 二级分类和商品属性之间添加单位选择框 -->
                      <div class="row-item">
                        <el-input-number v-model="row.quantity"  :min="0" placeholder="请输入数量"></el-input-number>
                      </div>
                      <!-- 单位选择框 -->
                      <div class="row-item">
                        <el-select 
                          v-model="row.unit" 
                          placeholder="单位" 
                          class="full-width"
                          :disabled="!row.feeName"
                          @change="() => handleUnitChange(row)">  <!-- 添加 change 事件 -->
                          <el-option 
                            v-for="item in row.unitOptions" 
                            :key="item.value" 
                            :label="item.label" 
                            :value="item.value">
                          </el-option>
                        </el-select>
                      </div>
                      <div class="row-item">
                        <el-cascader
                          placeholder="请选择规格"
                          v-model="row.attributes"
                          :options="row.transformedAttributeData"
                          :props="{
                            multiple: true,
                            emitPath: false
                          }"
                          :show-all-levels="false"
                          collapse-tags
                          collapse-tags-tooltip
                          clearable
                          style="width: 100%"
                        ></el-cascader>
                      </div>

                      <div class="row-item">
                        <el-button 
                          type="danger" 
                          icon="Delete" 
                          circle 
                          size="small" 
                          @click="removeJdTableRow(index)"
                          v-if="jdTableRows.length > 1">
                        </el-button>
                      </div>
                    </div>
                    
                    <div class="add-row">
                      <el-button
                        type="primary"
                        icon="Plus"
                        circle
                        size="default"
                        @click="addJdTableRow"
                        class="add-row-btn">
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
    </div>

    <div class="button-container">
      <el-button type="primary" @click="nextStep" :disabled="isReportGenerated">
         开始生成
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, QuestionFilled } from '@element-plus/icons-vue';
import { getAllProjects } from '@/api/xjzs/project';
import { getJdCategories, getProductAttributes, getTrainingCategories, getTrainingUnit } from '@/api/xjzs/trainingFee';

export default {
  name: 'ProjectSelectStep',
  components: {
    Plus,
    QuestionFilled
  },
  props: {
    formData: {
      type: Object,
      required: true
    },
    isReportGenerated: {
      type: Boolean,
      default: false
    }
  },
  emits: ['next-step', 'prev-step'],
  setup(props, { emit }) {
    const projectOptions = ref([]);
    const activeTableType = ref('standard');
    const shouldHighlightTable = ref(false);
    const isAutoFilling = ref(false); // 标记是否正在自动填充数据
    let highlightTimer = null; // 高亮定时器
    
    // 标准表数据
    const standardTable = reactive({
      scene: '',
      firstCategory: '',
      secondCategory: '',
      productCode: '',
      quantity: 0
    });
    
    // 工程咨询类标准表数据
    const engineeringTable = reactive({
      category: '',
      workResult: '',
      difficulty: '',
      staffing: '',
      evaluation: '',
      adjustmentFactor: '',
      facilityScale: '',
      designDepth: '',
      complexityFactor: '1.0',
      engineeringComplexity: '',
      budgetLimit: '',
    });

    // 工程咨询类别选项
    const engineeringCategoryOptions = [
      {
        value: '工程造价咨询',
        label: '工程造价咨询',
        children: [
          { value: '预算、结算审核', label: '预算、结算审核' },
          { value: '全过程跟踪审计', label: '全过程跟踪审计' },
          { value: '竣工决算审计', label: '竣工决算审计' }
        ]
      },
      { value: '房产物业评估咨询', label: '房产物业评估咨询' },
      { value: '工程项目监理', label: '工程项目监理' },
      { value: '工程项目设计', label: '工程项目设计' }
    ];

    // 专业调整系数选项
    const professionalAdjustmentOptions = [0.8, 1.0];

    // 工程复杂程度调整系数选项
    const complexityAdjustmentOptions = [
      { label: '一般（I级）  - 0.85', value: '0.85' },
      { label: '较复杂（Ⅱ级） - 1.0', value: '1.0' },
      { label: '复杂（Ⅲ级） - 1.15', value: '1.15' }
    ];

    // 高程调整系数选项
    const altitudeAdjustmentOptions = [
      { label: '海拔高程2001 m以下 - 1', value: '1' },
      { label: '海拔高程2001～3000 m - 1.1', value: '1.1' },
      { label: '海拔高程3001～3500 m - 1.2', value: '1.2' },
      { label: '海拔高程3501～4000 m - 1.3', value: '1.3' }
    ];
    
    // 标准表行数据列表
    const standardTableRows = ref([]);
    const engineeringTableRows = ref([]);

    // 初始化工程咨询类表格数据
    const initEngineeringTableRows = () => {
      engineeringTableRows.value = [{
        category: [],
        constructionCost: '',
        totalInvestment: '',
        landAndGovFees: '',
        evaluationValue: '',
        billingAmount: {
          constructionFee: '',
          equipmentFee: '',
          testRunFee: ''
        },
        professionalAdjustment: '',
        complexityAdjustment: '0.85',
        altitudeAdjustment: '1',
        additionalAdjustment: '',
        otherDesignFees: ''
      }];
    };

    // 获取工程咨询类型
    const getConsultingType = (row) => {
      const category = row.category || [];
      if (!category || category.length === 0) return '';
      return category[1] || category[0];
    };
    
    // 工程咨询类型常量
    const CONSULTING_TYPES = {
      BUDGET_AUDIT: '预算、结算审核',
      FULL_AUDIT: '全过程跟踪审计',
      COMPLETION_AUDIT: '竣工决算审计',
      PROPERTY_EVALUATION: '房产物业评估咨询',
      PROJECT_SUPERVISION: '工程项目监理',
      PROJECT_DESIGN: '工程项目设计'
    };
    
    // 字段显示规则配置
    const getFieldVisibility = (type) => {
      const config = {
        [CONSULTING_TYPES.BUDGET_AUDIT]: {
          constructionCost: true,
          totalInvestment: false,
          landAndGovFees: false,
          evaluationValue: false,
          billingAmount: false,
          professionalAdjustment: false,
          complexityAdjustment: false,
          altitudeAdjustment: false,
          additionalAdjustment: false,
          otherDesignFees: false
        },
        [CONSULTING_TYPES.FULL_AUDIT]: {
          constructionCost: false,
          totalInvestment: true,
          landAndGovFees: true,
          evaluationValue: false,
          billingAmount: false,
          professionalAdjustment: false,
          complexityAdjustment: false,
          altitudeAdjustment: false,
          additionalAdjustment: false,
          otherDesignFees: false
        },
        [CONSULTING_TYPES.COMPLETION_AUDIT]: {
          constructionCost: false,
          totalInvestment: true,
          landAndGovFees: false,
          evaluationValue: false,
          billingAmount: false,
          professionalAdjustment: false,
          complexityAdjustment: false,
          altitudeAdjustment: false,
          additionalAdjustment: false,
          otherDesignFees: false
        },
        [CONSULTING_TYPES.PROPERTY_EVALUATION]: {
          constructionCost: false,
          totalInvestment: false,
          landAndGovFees: false,
          evaluationValue: true,
          billingAmount: false,
          professionalAdjustment: false,
          complexityAdjustment: false,
          altitudeAdjustment: false,
          additionalAdjustment: false,
          otherDesignFees: false
        },
        [CONSULTING_TYPES.PROJECT_SUPERVISION]: {
          constructionCost: false,
          totalInvestment: false,
          landAndGovFees: false,
          evaluationValue: false,
          billingAmount: true,
          professionalAdjustment: true,
          complexityAdjustment: true,
          altitudeAdjustment: true,
          additionalAdjustment: false,
          otherDesignFees: false
        },
        [CONSULTING_TYPES.PROJECT_DESIGN]: {
          constructionCost: false,
          totalInvestment: false,
          landAndGovFees: false,
          evaluationValue: false,
          billingAmount: true,
          professionalAdjustment: true,
          complexityAdjustment: true,
          altitudeAdjustment: false,
          additionalAdjustment: true,
          otherDesignFees: true
        }
      };
      
      return config[type] || {
        constructionCost: false,
        totalInvestment: false,
        landAndGovFees: false,
        evaluationValue: false,
        billingAmount: false,
        professionalAdjustment: false,
        complexityAdjustment: false,
        altitudeAdjustment: false,
        additionalAdjustment: false,
        otherDesignFees: false
      };
    };

    // 检查是否应该显示列（避免闪烁）
    const shouldShowColumn = (fieldName) => {
      // 如果没有任何数据，显示所有列
      if (!engineeringTableRows.value || engineeringTableRows.value.length === 0) {
        return true;
      }
      
      // 检查是否有任何行需要显示该字段
      return engineeringTableRows.value.some(row => {
        const visibility = getFieldVisibility(getConsultingType(row));
        return visibility[fieldName];
      });
    };
    const handleEngineeringCategoryChange = (row) => {
      const type = getConsultingType(row);
      
      // 根据不同类型清空不需要的字段
      if (type === CONSULTING_TYPES.BUDGET_AUDIT) {
        row.totalInvestment = '';
        row.landAndGovFees = '';
        row.evaluationValue = '';
      } else if (type === CONSULTING_TYPES.PROPERTY_EVALUATION) {
        row.constructionCost = '';
        row.totalInvestment = '';
        row.landAndGovFees = '';
      }
    };

    // 京东慧采分类数据
    const jdCategories = reactive({
      feeNames: [],
      firstCategories: [],
      secondCategories: []
    });
    
    // 京东慧采表格行数据
    const jdTableRows = ref([]); // 初始化为空数组
    
    // 培训类相关的数据和方法
    const trainingTableRows = ref([]);  
    const trainingCategories = reactive({
      feeNames: [],
      firstCategories: [],
      secondCategories: [],
      teacherTitles: []
    });

    // 获取项目列表
    const loadProjects = async () => {
      try {
        const res = await getAllProjects();
        if (res && res.data) {
          projectOptions.value = res.data.data.map(item => ({
            value: item.id,
            label: item.name,
            project: item
          }));
        }
      } catch (error) {
        console.error('加载项目列表失败:', error);
        ElMessage.error('加载项目列表失败');
      }
    };

    // 解析项目描述中的培训信息
    const parseTrainingInfoFromDescription = (description) => {
      if (!description) return null;

      const trainingInfo = {};

      // 解析培训天数：优先匹配"培训天数：XX天"的模式
      const daysMatch = description.match(/培训天数[：:]\s*(\d+)\s*天/);
      if (daysMatch) {
        trainingInfo.training_days = parseInt(daysMatch[1]);
      }

      // 解析培训人数：优先匹配"培训人数：XX人"的模式
      const countMatch = description.match(/培训人数[：:]\s*(\d+)\s*人/);
      if (countMatch) {
        trainingInfo.trainee_count = parseInt(countMatch[1]);
      }

      return Object.keys(trainingInfo).length > 0 ? trainingInfo : null;
    };

    // 项目选择变更时更新表单数据
    const handleProjectChange = (projectId) => {
      if (!projectId) return;

      const selectedOption = projectOptions.value.find(p => p.value === projectId);
      if (selectedOption && selectedOption.project) {
        const project = selectedOption.project;

        // 将项目对象的属性赋值给formData
        props.formData.projectName = project.name;
        props.formData.projectCode = project.code || `P${project.id}`;
        props.formData.projectType = project.type || '';
        props.formData.projectCategory = project.category || '';
        props.formData.projectDescription = project.content || '';
        props.formData.procurementMethod = project.procurementMethod || '';
        props.formData.algorithmCategory = project.algorithmCategory || '';
        props.formData.id = project.id;

        console.log('项目选择后的算法类型:', props.formData.algorithmCategory);

        // 使用 nextTick 确保算法类型变化的监听器执行完毕后再解析培训信息
        nextTick(() => {
          if (props.formData.algorithmCategory === '培训类' && project.content) {
            // 延迟执行，确保表格初始化完成
            setTimeout(() => {
              const trainingInfo = parseTrainingInfoFromDescription(project.content);
              if (trainingInfo) {
                // 确保培训表格已初始化
                if (trainingTableRows.value.length === 0) {
                  initTrainingTableRows();
                }

                // 标记开始自动填充
                isAutoFilling.value = true;

                // 更新第一行的培训信息
                if (trainingTableRows.value.length > 0) {
                  const firstRow = trainingTableRows.value[0];
                  if (trainingInfo.training_days) {
                    firstRow.training_days = trainingInfo.training_days;
                  }
                  if (trainingInfo.trainee_count) {
                    firstRow.trainee_count = trainingInfo.trainee_count;
                  }
                }

                // 自动填充完成后重置标志，延迟更长时间确保所有异步操作完成
                setTimeout(() => {
                  isAutoFilling.value = false;
                }, 500);
              }
            }, 100); // 延迟100ms执行
          }
        });

        // 根据项目类型自动切换标准表
        if (props.formData.projectType === '工程咨询') {
          activeTableType.value = 'engineering';
        } else {
          activeTableType.value = 'standard';
        }

        // 项目选择后高亮提示用户填写标准表
        shouldHighlightTable.value = true;

        // 清除之前的定时器
        if (highlightTimer) {
          clearTimeout(highlightTimer);
        }

        // 3秒后自动取消高亮
        highlightTimer = setTimeout(() => {
          shouldHighlightTable.value = false;
          highlightTimer = null;
        }, 3000);
      }
    };
    
    // 监听项目选择变化
    watch(() => props.formData.selectedProject, (newVal) => {
      if (newVal) {
        handleProjectChange(newVal);
      }
    });

    // 监听算法类型变化，初始化对应的表格
    watch(() => props.formData.algorithmCategory, (newVal) => {
      if (newVal === '培训类') {
        // 只有在表格为空时才初始化，避免覆盖已解析的数据
        if (trainingTableRows.value.length === 0) {
          initTrainingTableRows();
        }
        fetchTrainingCategories();
      } else if (newVal === '工程咨询类') {
        // 初始化工程咨询类表格
        if (engineeringTableRows.value.length === 0) {
          initEngineeringTableRows();
        }
      } else {
        // 初始化京东慧采表格
        if (jdTableRows.value.length === 0) {
          addJdTableRow();
        }
        fetchJdCategories();
      }
    });

    // 监听表格数据变化，用户开始填写时取消高亮
    watch([trainingTableRows, engineeringTableRows, jdTableRows], () => {
      // 如果正在自动填充，不取消高亮
      if (isAutoFilling.value) {
        return;
      }

      // 检查是否有用户输入的数据
      const hasTrainingData = trainingTableRows.value.some(row =>
        row.training_type !== '线上培训' ||
        row.training_days !== 1 ||
        row.trainee_count !== 1 ||
        row.daily_hours !== 1 ||
        row.instructor_title !== '副高级职称' ||
        row.training_category !== '处级及以下（三类）'
      );

      const hasEngineeringData = engineeringTableRows.value.some(row =>
        row.category.length > 0 ||
        row.constructionCost ||
        row.totalInvestment ||
        row.landAndGovFees ||
        row.evaluationValue
      );

      const hasJdData = jdTableRows.value.some(row =>
        row.feeName ||
        row.firstCategory ||
        row.secondCategory ||
        row.quantity > 1
      );

      // 如果用户开始填写数据，取消高亮
      if (hasTrainingData || hasEngineeringData || hasJdData) {
        shouldHighlightTable.value = false;
        // 清除自动取消高亮的定时器
        if (highlightTimer) {
          clearTimeout(highlightTimer);
          highlightTimer = null;
        }
      }
    }, { deep: true });

    // 标准表类型切换
    const handleTableTypeChange = (tabName) => {
      activeTableType.value = tabName;
    };
    
    // 添加标准表行
    const addStandardTableRow = () => {
      if (!standardTable.scene || !standardTable.firstCategory || !standardTable.productCode || standardTable.quantity <= 0) {
        ElMessage.warning('请完整填写标准表信息');
        return;
      }
      
      standardTableRows.value.push({
        ...JSON.parse(JSON.stringify(standardTable)),
        id: Date.now() // 临时ID
      });
      
      // 将标准表数据保存到formData中
      props.formData.standardTableRows = standardTableRows.value;
      
      // 清空当前行数据，准备下一行输入
      standardTable.scene = '';
      standardTable.firstCategory = '';
      standardTable.secondCategory = '';
      standardTable.productCode = '';
      standardTable.quantity = 0;
      
      ElMessage.success('添加成功');
    };
    
    // 添加工程咨询类标准表行
    const addEngineeringTableRow = () => {
      engineeringTableRows.value.push({
        category: [],
        constructionCost: '',
        totalInvestment: '',
        landAndGovFees: '',
        evaluationValue: '',
        billingAmount: {
          constructionFee: '',
          equipmentFee: '',
          testRunFee: ''
        },
        professionalAdjustment: '',
        complexityAdjustment: '0.85',
        altitudeAdjustment: '1',
        additionalAdjustment: '',
        otherDesignFees: ''
      });
      
      ElMessage.success('添加成功');
    };

    // 删除工程咨询类标准表行
    const removeEngineeringTableRow = (index) => {
      engineeringTableRows.value.splice(index, 1);
      ElMessage.success('删除成功');
    };

    // 获取京东慧采分类数据
    const fetchJdCategories = async () => {
      try {
        const res = await getJdCategories();
        if (res.data.success) {
          const data = res.data.data;
          jdCategories.feeNames = data.feeNames || [];
          jdCategories.firstCategories = data.firstCategories || [];
          jdCategories.secondCategories = data.secondCategories || [];
        }
      } catch (error) {
        console.error('获取京东慧采分类数据失败:', error);
      }
    };
    
    // 处理费用名称变更
    const handleFeeNameChange = async (row) => {
      if (!row.feeName) {
        row.attributeGroups = [];
        row.attributes = [];
        row.transformedAttributeData = []; // 清空当前行的属性数据
        row.firstCategory = ''; // 清空一级分类
        row.secondCategory = ''; // 清空二级分类
        row.unit = ''; // 清空单位
        row.unitOptions = []; // 清空单位选项
        return;
      }
      
      try {
        // 从 jdCategories.feeNames 中找到选中的费用名称对应的分类数据
        const selectedFeeData = jdCategories.feeNames.find(item => item.value === row.feeName);
        if (selectedFeeData) {
          // 自动填充一级分类和二级分类
          if (selectedFeeData.firstCategories) {
            // 如果有多个分类，取第一个作为默认值
            const firstCategoryArray = selectedFeeData.firstCategories.split(',');
            row.firstCategory = firstCategoryArray[0];
          }
          
          if (selectedFeeData.secondCategories) {
            // 如果有多个分类，取第一个作为默认值
            const secondCategoryArray = selectedFeeData.secondCategories.split(',');
            row.secondCategory = secondCategoryArray[0];
          }
        }

        // 根据选择的费用名称获取相关数据
        const resUnit = await getTrainingUnit(row.feeName);
        if (resUnit.data.success) {
          // 自动填充相关字段
          const data = resUnit.data.data;
          if (data) {
            // 设置单位选项
            if (data) {
              row.unitOptions = data.map(item => ({
                label: item.unit,
                value: item.unit
              }));
              if (row.unitOptions.length > 0) {
                // 设置第一个单位为默认值
                row.unit = row.unitOptions[0].value;
                
                // 主动触发单位变更，加载商品属性
                await handleUnitChange(row);
              }
            } else {
              row.unitOptions = [];
              row.unit = '';
            }
          }
        }
        
        // 设置默认单位（如果还没有设置的话）
        if (!row.unit && row.unitOptions.length > 0) {
          row.unit = row.unitOptions[0].value;
          // 主动触发单位变更，加载商品属性
          await handleUnitChange(row);
        }
        
       
      } catch (error) {
        console.error('获取商品属性失败:', error);
      }
    };

    // 处理单位变更
    const handleUnitChange = async (row) => {
      if (!row.feeName || !row.unit) {
        return;
      }    
      try {
        // 根据费用名称和单位获取属性
        const res = await getProductAttributes(row.feeName, row.unit);
        if (res.data.success) {
          const data = res.data.data || [];
          row.attributeGroups = data;
          
          // 更新当前行的商品属性数据格式
          row.transformedAttributeData = transformAttributeGroups(row.attributeGroups);
          console.log('单位变更后获取商品属性:', row.transformedAttributeData);
          row.attributes = []; // 清空已选属性
        }
      } catch (error) {
        console.error('获取商品属性失败:', error);
      }
    };


    
    // 添加京东慧采表格行
    // 添加京东慧采表格行
    const addJdTableRow = () => {
    jdTableRows.value.push({
    feeName: '',
    firstCategory: '',
    secondCategory: '',
    unit: '', // 添加单位字段
    unitOptions: [], // 添加单位选项
    attributes: [],
    attributeGroups: [],
    transformedAttributeData: [], // 为每一行添加独立的属性数据
    quantity: 1
    });
    };
    
    // 删除京东慧采表格行
    const removeJdTableRow = (index) => {
      jdTableRows.value.splice(index, 1);
    };

    // 初始化培训类表格行
    const initTrainingTableRows = () => {
      trainingTableRows.value = [{
        training_type: '线上培训',
        training_days: 1,
        trainee_count: 1,
        daily_hours: 8,
        instructor_title: '副高级职称',
        training_category: '处级及以下（三类）'
      }];
    };

    // 添加培训类表格行
    const addTrainingTableRow = () => {
      trainingTableRows.value.push({
        training_type: '线上培训',
        training_days: 1,
        trainee_count: 1,
        daily_hours: 8,
        instructor_title: '副高级职称',
        training_category: '处级及以下（三类）'
      });
    };

    // 删除培训类表格行
    const removeTrainingTableRow = (index) => {
      trainingTableRows.value.splice(index, 1);
    };
    // 修改 transformAttributeGroups 方法，返回 Cascader 所需的格式
    const transformAttributeGroups = (attributeGroups) => {
      if (!attributeGroups || attributeGroups.length === 0) {
        return [];
      }
      
      const result = [];
      const groupedData = {};
      
      // 按 attributeName 分组
      attributeGroups.forEach(item => {
        const { attributeName, attributeValue } = item;
        if (!groupedData[attributeName]) {
          groupedData[attributeName] = new Set(); // 使用Set来避免重复值
        }
        groupedData[attributeName].add(attributeValue);
      });
      
      // 转换为 Cascader 所需的数组格式
      for (const key in groupedData) {
        const children = Array.from(groupedData[key]).map(value => ({
          value,
          label: value
        }));
        
        result.push({
          value: key,
          label: key,
          children
        });
      }
      
      return result;
    };
    // 获取培训类分类数据
    const fetchTrainingCategories = async () => {
      try {
        const res = await getTrainingCategories();
        if (res.data.success) {
          trainingCategories.feeNames = res.data.data.feeNames || [];
          trainingCategories.firstCategories = res.data.data.firstCategories || [];
          trainingCategories.secondCategories = res.data.data.secondCategories || [];
          trainingCategories.teacherTitles = res.data.data.teacherTitles || [];
          trainingCategories.units = res.data.data.units || []; // 新增单位数据
        }
      } catch (error) {
        console.error('获取培训类分类数据失败:', error);
      }
    };

    const nextStep = () => {
      // 验证是否选择了项目
      if (!props.formData.selectedProject) {
        ElMessage.warning('请先选择一个项目');
        return;
      }

      // 使用 nextTick 确保响应式更新完成后再进行验证
      nextTick(() => {
        // 验证是否配置了标准表
        let hasStandardTable = false;

        console.log('验证时的算法类型:', props.formData.algorithmCategory);

        if (props.formData.algorithmCategory === '培训类') {
        // 验证培训类标准表
        console.log('培训类数据验证:', trainingTableRows.value);

        // 检查是否有数据行
        if (trainingTableRows.value.length === 0) {
          ElMessage.warning('请至少添加一条培训类数据');
          return;
        }

        // 验证每行数据的完整性
        let isValid = true;
        let errorMessage = '';

        for (let i = 0; i < trainingTableRows.value.length; i++) {
          const row = trainingTableRows.value[i];

          if (!row.training_type) {
            errorMessage = `第${i + 1}行：请选择培训类型`;
            isValid = false;
            break;
          }

          if (!row.instructor_title) {
            errorMessage = `第${i + 1}行：请选择讲师职称`;
            isValid = false;
            break;
          }

          if (!row.training_category) {
            errorMessage = `第${i + 1}行：请选择培训类别`;
            isValid = false;
            break;
          }

          if (!row.daily_hours || row.daily_hours <= 0) {
            errorMessage = `第${i + 1}行：每天学时必须大于0`;
            isValid = false;
            break;
          }

          if (!row.training_days || row.training_days <= 0) {
            errorMessage = `第${i + 1}行：培训天数必须大于0`;
            isValid = false;
            break;
          }

          if (row.trainee_count === undefined || row.trainee_count <= 0) {
            errorMessage = `第${i + 1}行：学员数量必须大于0`;
            isValid = false;
            break;
          }
        }

        if (!isValid) {
          ElMessage.warning(errorMessage);
          return;
        }

        hasStandardTable = true;

        // 确保单位数据被保存
        trainingTableRows.value.forEach(row => {
          if (!row.unit && row.feeName) {
            // 如果没有设置单位但有费用名称，设置默认单位
            row.unit = '人天';
          }
        });

        // 保存培训类表格数据
        props.formData.trainingTableRows = trainingTableRows.value;
        console.log('保存的培训类数据:', props.formData.trainingTableRows);
      } else if (props.formData.algorithmCategory === '工程咨询类') {
        // 验证工程咨询类标准表
        hasStandardTable = engineeringTableRows.value.length > 0;
        if (hasStandardTable) {
          // 保存工程咨询类表格数据
          props.formData.engineeringTableRows = engineeringTableRows.value;
        }
      } else {
        // 验证京东慧采表格
        hasStandardTable = jdTableRows.value.length > 0 && jdTableRows.value.some(row => row.feeName);
        if (hasStandardTable) {
          // 只保存京东慧采表格数据的指定属性
          const filteredRows = jdTableRows.value.map(row => {
            // 确保单位数据存在
            if (!row.unit && row.feeName) {
              row.unit = '个';
            }
            
            // 只返回需要的属性
            return {
              feeName: row.feeName,
              firstCategory: row.firstCategory,
              secondCategory: row.secondCategory,
              quantity: row.quantity,
              unit: row.unit,
              attributes: row.attributes
            };
          });
          
          // 保存过滤后的京东慧采表格数据
          props.formData.jdTableRows = filteredRows;
        }
      }
      
        if (!hasStandardTable) {
          ElMessage.warning('请至少添加一条标准表数据');
          return;
        }

        emit('next-step');
      });
    };

    onMounted(() => {
      loadProjects();

      // 使用 nextTick 确保响应式数据完全初始化后再进行条件判断
      nextTick(() => {
        console.log('onMounted中的算法类型:', props.formData.algorithmCategory);

        // 根据当前算法类型初始化表格
        if (props.formData.algorithmCategory === '培训类') {
          // 如果已经有培训表格数据（详情模式回显），则使用现有数据
          if (props.formData.trainingTableRows && props.formData.trainingTableRows.length > 0) {
            trainingTableRows.value = [...props.formData.trainingTableRows];
          } else {
            initTrainingTableRows();
          }
          fetchTrainingCategories();
        } else if (props.formData.algorithmCategory === '工程咨询类') {
          // 如果已经有工程咨询类表格数据，则使用现有数据
          if (props.formData.engineeringTableRows && props.formData.engineeringTableRows.length > 0) {
            engineeringTableRows.value = [...props.formData.engineeringTableRows];
          } else {
            initEngineeringTableRows();
          }
        } else {
          // 如果已经有京东慧采表格数据，则使用现有数据
          if (props.formData.jdTableRows && props.formData.jdTableRows.length > 0) {
            jdTableRows.value = [...props.formData.jdTableRows];
          } else {
            addJdTableRow();
          }
          fetchJdCategories();
        }
      });
    });

    return {
      projectOptions,
      activeTableType,
      standardTable,
      engineeringTable,
      standardTableRows,
      engineeringTableRows,
      shouldHighlightTable,
      handleTableTypeChange,
      addStandardTableRow,
      addEngineeringTableRow,
      removeEngineeringTableRow,
      nextStep,
      // 工程咨询类相关
      engineeringCategoryOptions,
      professionalAdjustmentOptions,
      complexityAdjustmentOptions,
      altitudeAdjustmentOptions,
      getConsultingType,
      getFieldVisibility,
      shouldShowColumn,
      handleEngineeringCategoryChange,
      initEngineeringTableRows,
      // 京东慧采相关
      jdCategories,
      jdTableRows,
      handleFeeNameChange,
      addJdTableRow,
      removeJdTableRow,
      fetchJdCategories,
      // 培训类相关
      trainingTableRows,
      trainingCategories,
      addTrainingTableRow,
      removeTrainingTableRow,
      initTrainingTableRows,
      fetchTrainingCategories,
      transformAttributeGroups,
      handleUnitChange,
      // 项目选择相关
      handleProjectChange,
      parseTrainingInfoFromDescription
    };
  }
}
</script>

<style scoped>
.step-content {
  padding: 10px;
}

.step-content h3 {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.half-width {
  flex: 1;
}

.full-width {
  width: 100%;
}

/* 标准表样式 */
.standard-table-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* 高亮表格容器 */
.table-highlighted {
  border: 2px solid #409eff !important;
  box-shadow: 0 0 15px rgba(64, 158, 255, 0.4);
  animation: gentle-pulse 3s ease-in-out infinite;
}

/* 更柔和的高亮提示动画 */
@keyframes gentle-pulse {
  0%, 100% {
    box-shadow: 0 0 15px rgba(64, 158, 255, 0.4);
    border-color: #409eff;
  }
  50% {
    box-shadow: 0 0 25px rgba(64, 158, 255, 0.6);
    border-color: #66b1ff;
  }
}

/* 高亮表单项 */
.highlight-table {
  position: relative;
}

/* 高亮提示信息 */
.highlight-tip {
  margin-bottom: 15px;
}

/* 淡入淡出过渡动画 */
.fade-slide-enter-active {
  transition: all 0.5s ease-out;
}

.fade-slide-leave-active {
  transition: all 0.4s ease-in;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
  margin-bottom: 0;
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px);
  max-height: 0;
  margin-bottom: 0;
}

.fade-slide-enter-to,
.fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0);
  max-height: 100px;
  margin-bottom: 15px;
}

/* 自定义提示框样式 */
.highlight-alert {
  border-left: 4px solid #409eff;
  background-color: #f0f9ff;
}

.table-selector {
  padding: 10px;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 10px 0;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
}

.header-item {
  flex: 1;
  padding: 0 10px;
  text-align: center;
  font-size: 14px;
}

.table-row {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.row-item {
  flex: 1;
  padding: 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 所有输入组件居中样式 */
.row-item .el-input,
.row-item .el-select,
.row-item .el-cascader,
.row-item .el-input-number {
  width: 100%;
}

.row-item .el-input__inner,
.row-item .el-select .el-input__inner,
.row-item .el-cascader .el-input__inner,
.row-item .el-input-number .el-input__inner {
  text-align: center;
  height: 32px;
  line-height: 32px;
}

.row-item .el-input-number {
  width: 100%;
}

.row-item .el-input-number .el-input-number__decrease,
.row-item .el-input-number .el-input-number__increase {
  text-align: center;
}

.add-row {
  padding: 15px;
  text-align: center;
  background-color: #fafafa;
  border-top: 1px dashed #e4e7ed;
  transition: background-color 0.3s ease;
}

.add-row:hover {
  background-color: #f0f9ff;
}

.add-row-btn {
  border: 2px dashed #409eff;
  background-color: transparent;
  color: #409eff;
  transition: all 0.3s ease;
  box-shadow: none;
}

.add-row-btn:hover {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.add-row-btn:focus {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

/* 工程咨询类标准表样式 */
.engineering-table {
  margin-top: 20px;
}

.engineering-table .el-table {
  width: 100%;
}

.engineering-table .el-table__body-wrapper {
  overflow-x: auto;
}

/* 确保表格列占满宽度 */
.engineering-table .el-table th,
.engineering-table .el-table td {
  padding: 8px 4px;
  text-align: center;
}

/* 表格内容居中显示 */
.engineering-table .el-table .cell {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.engineering-table .el-input,
.engineering-table .el-select,
.engineering-table .el-cascader,
.engineering-table .el-input-number {
  width: 100%;
}

/* 确保输入框内的文字也居中 */
.engineering-table .el-input__inner,
.engineering-table .el-select .el-input__inner,
.engineering-table .el-cascader .el-input__inner,
.engineering-table .el-input-number .el-input__inner {
  text-align: center;
  height: 32px;
  line-height: 32px;
}

/* 数字输入框居中 */
.engineering-table .el-input-number {
  width: 100%;
}

.engineering-table .el-input-number .el-input-number__decrease,
.engineering-table .el-input-number .el-input-number__increase {
  text-align: center;
}

.button-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.button-container .el-button {
  min-width: 100px;
}

/* 项目信息标签样式 */
.project-info-tags {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.tag-row {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
  align-items: center;
}

.tag-row:last-child {
  margin-bottom: 0;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.tag-item.full-width {
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.tag-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  white-space: nowrap;
}

.description-content {
  padding: 8px 12px;
  /* background-color: #ffffff; */
  /* border: 1px solid #dcdfe6; */
  /* border-radius: 4px; */
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  width: 100%;
  min-height: 60px;
  word-wrap: break-word;
  box-sizing: border-box;
  max-width: 100%;
  overflow-wrap: break-word;
}
</style>



