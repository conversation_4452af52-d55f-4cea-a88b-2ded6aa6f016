# 详情模式数据回显问题修复说明

## 问题描述
在详情模式下，点击"查看详情"按钮后：
1. `onMounted` 中 `props.formData.algorithmCategory` 为空
2. `props.formData.trainingTableRows` 为空
3. 表格数据无法正确回显

## 问题原因
这是由于数据加载时序问题导致的：

1. **异步数据加载**：在 `projectAssistantWaterfall.vue` 中，数据是分步异步加载的
   - 先加载项目基本信息（包括 `algorithmCategory`）
   - 然后加载报告数据（包括表格数据）

2. **组件初始化时机**：`ProjectSelectStep` 组件的 `onMounted` 在数据加载完成之前就执行了

3. **监听器触发时机**：当 `algorithmCategory` 变化时，表格数据可能还没有加载完成

## 解决方案

### 1. 增强监听器逻辑
```javascript
// 监听算法类型变化，延迟初始化
watch(() => props.formData.algorithmCategory, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    // 延迟执行，等待可能的表格数据加载完成
    setTimeout(() => {
      initializeTableByAlgorithmType(newVal);
    }, 100);
  }
});

// 监听各种表格数据变化
watch(() => props.formData.trainingTableRows, (newVal) => {
  if (newVal && newVal.length > 0 && props.formData.algorithmCategory === '培训类') {
    trainingTableRows.value = [...newVal];
  }
}, { deep: true });

// 监听整个 formData 的变化，用于详情模式的完整数据回显
watch(() => props.formData, (newVal) => {
  // 检查是否是详情模式的数据加载完成
  if (newVal.algorithmCategory && newVal.id && 
      (newVal.trainingTableRows?.length > 0 || 
       newVal.engineeringTableRows?.length > 0 || 
       newVal.jdTableRows?.length > 0)) {
    console.log('检测到详情模式数据完整加载，重新初始化表格');
    initializeTableByAlgorithmType(newVal.algorithmCategory);
  }
}, { deep: true });
```

### 2. 提取表格初始化逻辑
```javascript
const initializeTableByAlgorithmType = (algorithmType) => {
  console.log('初始化表格类型:', algorithmType);
  
  if (algorithmType === '培训类') {
    // 优先使用现有数据（详情模式回显）
    if (props.formData.trainingTableRows && props.formData.trainingTableRows.length > 0) {
      console.log('使用现有培训表格数据');
      trainingTableRows.value = [...props.formData.trainingTableRows];
    } else if (trainingTableRows.value.length === 0) {
      console.log('初始化空培训表格');
      initTrainingTableRows();
    }
    fetchTrainingCategories();
  }
  // ... 其他类型的处理
};
```

### 3. 简化 onMounted 逻辑
```javascript
onMounted(() => {
  loadProjects();

  nextTick(() => {
    console.log('onMounted中的算法类型:', props.formData.algorithmCategory);
    console.log('onMounted中的培训表格数据:', props.formData.trainingTableRows);

    // 如果有算法类型，说明是详情模式或者已有数据，进行初始化
    if (props.formData.algorithmCategory) {
      initializeTableByAlgorithmType(props.formData.algorithmCategory);
    }
  });
});
```

## 调试信息
添加了以下调试日志：
- `console.log('算法类型变化:', { oldVal, newVal });`
- `console.log('培训表格数据变化:', newVal);`
- `console.log('检测到详情模式数据完整加载，重新初始化表格');`
- `console.log('初始化表格类型:', algorithmType);`
- `console.log('使用现有培训表格数据');`

## 验证步骤
1. 在管理首页点击"查看详情"按钮
2. 查看浏览器控制台的调试信息
3. 确认以下内容：
   - 算法类型正确识别
   - 表格数据正确回显
   - 表格能正常显示和操作

## 数据流程
```
详情模式数据加载流程：
1. 用户点击"查看详情" → 跳转到详情页面
2. projectAssistantWaterfall.vue onMounted 执行
3. 加载项目基本信息 → algorithmCategory 有值
4. ProjectSelectStep 监听到 algorithmCategory 变化 → 延迟初始化
5. 加载报告数据 → trainingTableRows 等有值
6. ProjectSelectStep 监听到完整数据 → 重新初始化表格
7. 表格正确显示数据
```

## 注意事项
- 使用了多层监听器确保在不同时机都能正确处理数据
- 延迟执行避免了数据加载时序问题
- 深度监听确保能捕获到嵌套数据的变化
- 条件判断确保只在必要时进行初始化，避免重复操作
