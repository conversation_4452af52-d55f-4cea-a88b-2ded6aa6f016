# EngineCalculationStep 详情模式回显修复说明

## 问题描述
在详情模式下，`EngineCalculationStep` 组件会自动开始新的计算，而不是显示已有的计算结果。这导致：
1. 用户看不到之前的计算过程
2. 重复计算浪费资源
3. 可能覆盖原有的计算结果

## 解决方案

### 1. 添加 isDetailMode 属性
```javascript
props: {
  formData: {
    type: Object,
    required: true
  },
  isDetailMode: {
    type: Boolean,
    default: false
  }
}
```

### 2. 修改 onMounted 逻辑
在详情模式下直接回显数据，而不是开始新的计算：

```javascript
onMounted(() => {
  console.log('EngineCalculationStep 组件挂载');
  console.log('详情模式:', props.isDetailMode);

  if (props.isDetailMode) {
    // 详情模式：直接回显已有的计算结果
    console.log('详情模式：开始回显计算结果');
    
    // 1. 使用已有的计算结果
    if (props.formData.calculationResult) {
      calculationResult.value = props.formData.calculationResult;
    }
    
    // 2. 从报告数据中提取计算过程
    if (props.formData.reportData) {
      try {
        let reportContent = null;
        
        // 尝试从不同字段获取报告内容
        if (props.formData.reportData.reportContent) {
          reportContent = typeof props.formData.reportData.reportContent === 'string'
            ? JSON.parse(props.formData.reportData.reportContent)
            : props.formData.reportData.reportContent;
        }
        
        // 提取计算过程文本用于显示
        if (reportContent && reportContent.calculationProcess) {
          const calculationProcess = Array.isArray(reportContent.calculationProcess) 
            ? reportContent.calculationProcess.join('\n\n')
            : reportContent.calculationProcess;
          streamContent.value = calculationProcess;
        }
      } catch (error) {
        console.error('解析报告内容失败:', error);
      }
    }
    
    // 3. 创建默认显示（如果没有数据）
    if (!calculationResult.value && !streamContent.value) {
      streamContent.value = '## 计算结果\n\n计算已完成，详细过程请查看报告。';
    }
  } else {
    // 非详情模式：正常开始计算
    setTimeout(() => {
      startStreamCalculationNew();
    }, 1000);
  }
});
```

### 3. 添加数据变化监听器
处理详情模式下数据的动态加载：

```javascript
// 监听详情模式下的数据变化
watch(() => props.formData.reportData, (newVal) => {
  if (props.isDetailMode && newVal && !streamContent.value) {
    console.log('检测到报告数据变化，重新回显');
    // 重新执行回显逻辑
    // ...
  }
}, { deep: true });

// 监听计算结果变化
watch(() => props.formData.calculationResult, (newVal) => {
  if (props.isDetailMode && newVal && !calculationResult.value) {
    console.log('检测到计算结果变化，更新显示');
    calculationResult.value = newVal;
  }
}, { deep: true });
```

### 4. 更新父组件传递属性
在 `projectAssistantWaterfall.vue` 和 `projectAssistant.vue` 中传递 `isDetailMode`：

```vue
<EngineCalculationStep
  :formData="formData"
  :isDetailMode="isDetailMode"
  @next-step="handleNextStep(1)"
/>
```

## 数据回显逻辑

### 数据来源优先级
1. **formData.calculationResult** - 直接的计算结果对象
2. **formData.reportData.reportContent** - 报告内容中的计算过程
3. **formData.reportData.content** - 备用的内容字段
4. **默认显示** - 如果没有任何数据，显示默认消息

### 显示内容
- **streamContent** - 显示计算过程的 Markdown 内容
- **calculationResult** - 显示最终的计算结果和价格

## 调试信息
添加了详细的调试日志：
- `console.log('EngineCalculationStep 组件挂载');`
- `console.log('详情模式:', props.isDetailMode);`
- `console.log('详情模式：开始回显计算结果');`
- `console.log('使用 formData.calculationResult:', calculationResult.value);`
- `console.log('从报告内容中提取计算过程:', reportContent);`
- `console.log('设置流式内容用于显示:', streamContent.value.substring(0, 200) + '...');`

## 验证步骤
1. 在管理首页点击"查看详情"按钮
2. 跳转到详情页面的引擎计算步骤
3. 确认以下内容：
   - 不会自动开始新的计算
   - 能正确显示之前的计算过程
   - 能正确显示计算结果和价格
   - 页面加载速度快（无需等待计算）

## 注意事项
- 详情模式下不会触发新的计算，只会显示已有数据
- 如果数据加载是异步的，监听器会处理数据变化
- 支持多种数据格式（JSON字符串或对象）
- 提供了默认显示以防止空白页面

## 兼容性
- 保持了原有的非详情模式功能不变
- 新增的 `isDetailMode` 属性有默认值，不会影响现有调用
- 监听器只在详情模式下生效，不会影响正常计算流程
