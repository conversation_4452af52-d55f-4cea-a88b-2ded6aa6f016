<template>
  <basic-container>
    <div class="project-assistant-container">
      <div class="assistant-header">
        <h2>编制项目最高限价</h2>
        <p class="subtitle">通过AI智能分析，快速生成项目最高限价</p>
      </div>

      <!-- 步骤条 -->
      <el-steps :active="activeStep" finish-status="success" class="steps-container">
        <el-step title="选择项目" description=""></el-step>
        <el-step title="选择计算方式" description=""></el-step>
        <el-step title="引擎计算" description=""></el-step>
        <el-step title="生成报告" description=""></el-step>
      </el-steps>

      <!-- 步骤内容区域 -->
      <div class="step-content-container">
        <!-- 步骤1：选择项目 -->
        <div v-if="activeStep === 1">
          <ProjectSelectStep
            :formData="formData"
            :isReportGenerated="isReportGenerated"
            @next-step="nextStep"
            @prev-step="prevStep" />
        </div>

        <!-- 步骤2：选择计算方式 -->
        <div v-if="activeStep === 2" class="step-content">
          <CalculationMethodStep
            :formData="formData"
            @next-step="nextStep"
            @prev-step="prevStep"
          />
        </div>

        <!-- 步骤3：引擎计算 -->
        <div v-if="activeStep === 3" class="step-content">
          <EngineCalculationStep
            :formData="formData"
            @next-step="nextStep"
            @prev-step="prevStep"
          />
        </div>

        <!-- 步骤4：生成报告 -->
        <!-- 步骤4：生成报告 -->
        <div v-if="activeStep === 4" class="step-content">
          <GenerateReportStep
            :formData="formData"
            :isDetailMode="isDetailMode"
            @prev-step="prevStep"
            @finish="finishProcess"
            @report-generated="handleReportGenerated"
          />
        </div>
      </div>
    </div>
  </basic-container>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router'; // 添加导入
import { ElMessage } from 'element-plus';
import { UploadFilled, Download, Plus as PlusIcon, Check, Clock, TrendCharts, Document, User, SetUp } from '@element-plus/icons-vue';
import ProjectSelectStep from './components/ProjectSelectStep.vue';
import CalculationMethodStep from './components/CalculationMethodStep.vue';
import EngineCalculationStep from './components/EngineCalculationStep.vue';
import GenerateReportStep from './components/GenerateReportStep.vue';
import { getJdCategories, getProductAttributes, getTrainingCategories, getTrainingAttributes } from '@/api/xjzs/trainingFee';
import { getDetail } from '@/api/xjzs/project'; // 添加导入
import { getProjectReport } from '@/api/xjzs/projectReport'; // 添加导入
import { Plus, Delete } from '@element-plus/icons-vue';

export default {
  name: 'ProjectAssistant',
  components: {
    Check,
    Clock,
    TrendCharts,
    Document,
    User,
    SetUp,
    ProjectSelectStep,
    CalculationMethodStep,
    EngineCalculationStep,
    GenerateReportStep
  },
  setup() {
    const activeStep = ref(1);
    const route = useRoute();
    const isDetailMode = ref(false); // 添加详情模式标志
    const isReportGenerated = ref(false); // 添加报告生成状态
    
    const formData = reactive({
      selectedProject: '',
      projectName: '',
      id: '',
      projectType: '',
      projectCategory: '',
      projectDescription: '',
      projectFiles: [],
      procurementMethod: '',
      algorithmCategory: '',
      calculationMethod: '',
      // 初始化标准表数据数组
      standardTableRows: [],
      engineeringTableRows: [],
      trainingTableRows: [],
      // 添加报告数据字段
      calculationResult: null,
      reportData: null
    });



    // 项目选择变更时更新表单数据
    const handleProjectChange = (projectId) => {
      if (!projectId) return;
      
      const selectedOption = projectOptions.value.find(p => p.value === projectId);
      if (selectedOption && selectedOption.project) {
        const project = selectedOption.project;
        
        // 将项目对象的属性赋值给formData
        formData.projectName = project.name;
        formData.projectCode = project.code || `P${project.id}`;
        formData.projectType = project.type || '';
        formData.projectCategory = project.category || '';
        formData.projectDescription = project.content || '';
        formData.procurementMethod = project.procurementMethod || '';
        formData.algorithmCategory = project.algorithmCategory || '';
        formData.id = project.id;
        
        // 可以根据需要添加更多属性
        
        // 根据项目类型自动切换标准表
        if (formData.projectType === '工程咨询') {
          activeTableType.value = 'engineering';
        } else {
          activeTableType.value = 'standard';
        }
      }
    };
    

    const handleTableTypeChange = (tabName) => {
      activeTableType.value = tabName;
    };
    
    // 添加标准表行
    const addStandardTableRow = () => {
      if (!standardTable.scene || !standardTable.firstCategory || !standardTable.productCode || standardTable.quantity <= 0) {
        ElMessage.warning('请完整填写标准表信息');
        return;
      }
      
      standardTableRows.value.push({
        ...JSON.parse(JSON.stringify(standardTable)),
        id: Date.now() // 临时ID
      });
      
      // 清空当前行数据，准备下一行输入
      standardTable.scene = '';
      standardTable.firstCategory = '';
      standardTable.secondCategory = '';
      standardTable.productCode = '';
      standardTable.quantity = 0;
      
      ElMessage.success('添加成功');
    };
    
    // 添加工程咨询类标准表行
    const addEngineeringTableRow = () => {
      if (!engineeringTable.category || !engineeringTable.workResult) {
        ElMessage.warning('请至少填写类别和工作成果');
        return;
      }
      
      engineeringTableRows.value.push({
        ...JSON.parse(JSON.stringify(engineeringTable)),
        id: Date.now() // 临时ID
      });
      
      // 清空部分字段，保留一些常用值
      engineeringTable.workResult = '';
      engineeringTable.difficulty = '';
      engineeringTable.staffing = '';
      engineeringTable.evaluation = '';
      engineeringTable.budgetLimit = '';
      
      ElMessage.success('添加成功');
    };

    // 选择计算方式
    const selectCalculationMethod = (method) => {
      formData.calculationMethod = method;
      ElMessage.success(`已选择${getCalculationMethodName(method)}计算方式`);
    };
    
    // 获取计算方式的中文名称
    const getCalculationMethodName = (method) => {
      const methodNames = {
        'government': '政府、行业指导价格法',
        'cost': '成本核算法',
        'historical': '历史价格法',
        'market': '市场调研法',
        'comprehensive': '综合定价法'
      };
      return methodNames[method] || '未选择';
    };

    // 验证培训类标准表是否完整填写
    const validateTrainingTable = () => {
      if (trainingTableRows.value.length === 0) {
        ElMessage.warning('请至少添加一条培训类标准表数据');
        return false;
      }
      
      for (let i = 0; i < trainingTableRows.value.length; i++) {
        const row = trainingTableRows.value[i];
        if (!row.trainingLocation) {
          ElMessage.warning(`第${i+1}行培训类标准表的培训场景不能为空`);
          return false;
        }
        if (!row.teacherTitle) {
          ElMessage.warning(`第${i+1}行培训类标准表的师资职称不能为空`);
          return false;
        }
        if (!row.hoursPerDay || row.hoursPerDay <= 0) {
          ElMessage.warning(`第${i+1}行培训类标准表的每天学时必须大于0`);
          return false;
        }
        if (!row.trainingDays || row.trainingDays <= 0) {
          ElMessage.warning(`第${i+1}行培训类标准表的培训天数必须大于0`);
          return false;
        }
        if (row.trainingPeople === undefined || row.trainingPeople === null || row.trainingPeople < 0) {
          ElMessage.warning(`第${i+1}行培训类标准表的培训人数不能为空且不能小于0`);
          return false;
        }
      }
      
      return true;
    };

    // 验证工程咨询类标准表是否完整填写
    const validateEngineeringTable = () => {
      if (engineeringTableRows.value.length === 0) {
        ElMessage.warning('请至少添加一条工程咨询类标准表数据');
        return false;
      }
      
      for (let i = 0; i < engineeringTableRows.value.length; i++) {
        const row = engineeringTableRows.value[i];
        if (!row.category) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的工程咨询类别不能为空`);
          return false;
        }
        if (!row.workResult) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的编制完成工作成果不能为空`);
          return false;
        }
        if (!row.difficulty) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的编制技术难度不能为空`);
          return false;
        }
        if (!row.staffing) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的工程人员配置情况不能为空`);
          return false;
        }
        if (!row.evaluation) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的评估值不能为空`);
          return false;
        }
        if (!row.adjustmentFactor) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的调整系数不能为空`);
          return false;
        }
        if (!row.facilityScale) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的设施规模不能为空`);
          return false;
        }
        if (!row.designDepth) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的设计深度不能为空`);
          return false;
        }
        if (!row.complexityFactor) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的专业复杂系数不能为空`);
          return false;
        }
        if (!row.engineeringComplexity) {
          ElMessage.warning(`第${i+1}行工程咨询类标准表的工程复杂程度影响系数不能为空`);
          return false;
        }
      }
      
      return true;
    };

    // 验证京东慧采标准表是否完整填写
    const validateJdTable = () => {
      if (jdTableRows.value.length === 0) {
        ElMessage.warning('请至少添加一条京东慧采标准表数据');
        return false;
      }
      
      for (let i = 0; i < jdTableRows.value.length; i++) {
        const row = jdTableRows.value[i];
        if (!row.feeName) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的费用名称不能为空`);
          return false;
        }
        if (!row.firstCategory) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的一级分类不能为空`);
          return false;
        }
        if (!row.secondCategory) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的二级分类不能为空`);
          return false;
        }
        if (!row.attributes || row.attributes.length === 0) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的商品属性不能为空`);
          return false;
        }
        if (!row.quantity || row.quantity <= 0) {
          ElMessage.warning(`第${i+1}行京东慧采标准表的数量必须大于0`);
          return false;
        }
      }
      
      return true;
    };

    const nextStep = () => {
      if (activeStep.value === 2 && !formData.calculationMethod) {
        ElMessage.warning('请选择一种计算方式');
        return;
      }
      
      if (activeStep.value < 4) {
        activeStep.value += 1;
      }
    };

    const prevStep = () => {
      if (activeStep.value > 1) {
        activeStep.value -= 1;
      }
    };

    const goBack = () => {
      // 返回上一页或首页
      history.back();
    };

    const finishProcess = () => {
      ElMessage.success('项目最高限价编制完成！');
      // 可以跳转到其他页面或重置流程
      activeStep.value = 1;
    };

    // 处理报告生成完成事件
    const handleReportGenerated = () => {
      isReportGenerated.value = true;
    };

    onMounted(async () => {
      // 检查URL参数
      const { projectId, step, mode } = route.query;
      
      if (projectId) {
        // 设置选中的项目ID
        formData.id = projectId;
        formData.selectedProject = projectId;
        
        // 设置详情模式标志
        if (mode === 'detail') {
          isDetailMode.value = true;
          
          try {
            // 使用 getDetail 接口获取项目详情
            const projectRes = await getDetail(projectId);
            if (projectRes.data && projectRes.data.success) {
              const projectData = projectRes.data.data;
              
              // 将项目对象的属性赋值给formData
              formData.projectName = projectData.name;
              formData.projectCode = projectData.code || `P${projectData.id}`;
              formData.projectType = projectData.type || '';
              formData.projectCategory = projectData.category || '';
              formData.projectDescription = projectData.content || '';
              formData.procurementMethod = projectData.procurementMethod || '';
              formData.algorithmCategory = projectData.algorithmCategory || '';
              formData.id = projectData.id;
              
              // 获取项目报告详情
              const reportRes = await getProjectReport(projectId);
              if (reportRes.data && reportRes.data.success) {
                formData.reportData = reportRes.data.data;
                // 如果有报告数据，说明报告已生成
                isReportGenerated.value = true;
                
                // 如果报告内容是JSON字符串，则解析它
                if (formData.reportData && formData.reportData.reportContent) {
                  try {
                    const reportContent = JSON.parse(formData.reportData.reportContent);
                    
                    // 将报告内容中的数据合并到formData中
                    if (reportContent.formData) {
                      // 合并计算方法
                      formData.calculationMethod = reportContent.formData.calculationMethod || formData.calculationMethod;
                      
                      // 合并计算结果
                      formData.calculationResult = reportContent.formData.calculationResult || null;
                      
                      // 合并标准表数据
                      if (formData.algorithmCategory === '培训类' && reportContent.formData.trainingTableRows) {
                        formData.trainingTableRows = reportContent.formData.trainingTableRows;
                      } else if (formData.algorithmCategory === '工程咨询类' && reportContent.formData.engineeringTableRows) {
                        formData.engineeringTableRows = reportContent.formData.engineeringTableRows;
                      } else if (reportContent.formData.standardTableRows) {
                        formData.standardTableRows = reportContent.formData.standardTableRows;
                      }
                    }
                  } catch (error) {
                    console.error('解析报告内容失败:', error);
                  }
                }
              }
            }
          } catch (error) {
            console.error('获取项目详情失败:', error);
            ElMessage.error('获取项目详情失败: ' + (error.message || '未知错误'));
          }
        } else {
          // 非详情模式，使用原有的处理方式
          handleProjectChange(projectId);
        }
        
        // 如果指定了步骤，直接跳转到该步骤
        if (step && !isNaN(parseInt(step))) {
          activeStep.value = parseInt(step);
        }
      }
      
      // 根据当前算法类型初始化表格
      if (formData.algorithmCategory === '培训类') {
        if (!isDetailMode.value) { // 只在非详情模式下初始化
          initTrainingTableRows();
        }
        fetchTrainingCategories();
      } else if (formData.algorithmCategory === '工程咨询类') {
        // 初始化工程咨询类表格，只在非详情模式下初始化
      } else {
        if (!isDetailMode.value) { // 只在非详情模式下初始化
          addJdTableRow();
        }
        fetchJdCategories();
      }
    });

    // 京东慧采分类数据
    const jdCategories = reactive({
      feeNames: [],
      firstCategories: [],
      secondCategories: []
    });
    
    // 京东慧采表格行数据
    const jdTableRows = ref([
      {
        feeName: '',
        firstCategory: '',
        secondCategory: '',
        attributes: [],
        attributeGroups: [],
        quantity: 1
      }
    ]);
    
    // 获取京东慧采分类数据
    const fetchJdCategories = async () => {
      try {
        const res = await getJdCategories();
        if (res.data.success) {
          const data = res.data.data;
          jdCategories.feeNames = data.feeNames || [];
          jdCategories.firstCategories = data.firstCategories || [];
          jdCategories.secondCategories = data.secondCategories || [];
        }
      } catch (error) {
        console.error('获取京东慧采分类数据失败:', error);
      }
    };
    
    // 处理费用名称变更
    const handleFeeNameChange = async (row) => {
      if (!row.feeName) {
        row.attributeGroups = [];
        row.attributes = [];
        return;
      }
      
      try {
        const res = await getProductAttributes(row.feeName);
        if (res.data.success) {
          row.attributeGroups = res.data.data || [];
          row.attributes = []; // 清空已选属性
        }
      } catch (error) {
        console.error('获取商品属性失败:', error);
      }
    };
    
    // 添加京东慧采表格行
    const addJdTableRow = () => {
      jdTableRows.value.push({
        feeName: '',
        firstCategory: '',
        secondCategory: '',
        attributes: [],
        attributeGroups: [],
        quantity: 1
      });
    };
    
    // 删除京东慧采表格行
    const removeJdTableRow = (index) => {
      jdTableRows.value.splice(index, 1);
    };
    
    // 培训类相关的数据和方法
    const trainingTableRows = ref([]);
    const trainingCategories = reactive({
      feeNames: [],
      firstCategories: [],
      secondCategories: [],
      teacherTitles: []
    });

    // 初始化培训类表格行
    const initTrainingTableRows = () => {
      trainingTableRows.value = [{
        trainingLocation: '线下',
        teacherTitle: '',
        hoursPerDay: 8,
        trainingDays: 1,
        trainingPeople: 0
      }];
    };

    // 添加培训类表格行
    const addTrainingTableRow = () => {
      trainingTableRows.value.push({
        trainingLocation: '线下',
        teacherTitle: '',
        hoursPerDay: 8,
        trainingDays: 1,
        trainingPeople: 0
      });
    };

    // 删除培训类表格行
    const removeTrainingTableRow = (index) => {
      trainingTableRows.value.splice(index, 1);
    };

    // 处理培训类费用名称变更
    const handleTrainingFeeNameChange = async (row) => {
      if (!row.feeName) {
        return;
      }
      
      try {
        // 根据选择的费用名称获取相关数据
        const res = await getTrainingAttributes(row.feeName);
        if (res.data.success) {
          // 自动填充相关字段
          const data = res.data.data;
          if (data) {
            // 设置师资职称
            if (data.teacherTitle) {
              row.teacherTitle = data.teacherTitle;
            }
            
            // 设置一级分类
            if (data.firstCategory) {
              row.firstCategory = data.firstCategory;
            }
            
            // 设置二级分类
            if (data.secondCategory) {
              row.secondCategory = data.secondCategory;
            }
          }
        }
      } catch (error) {
        console.error('获取培训属性失败:', error);
      }
    };

    // 获取培训类分类数据
    const fetchTrainingCategories = async () => {
      try {
        const res = await getTrainingCategories();
        if (res.data.success) {
          trainingCategories.feeNames = res.data.data.feeNames || [];
          trainingCategories.firstCategories = res.data.data.firstCategories || [];
          trainingCategories.secondCategories = res.data.data.secondCategories || [];
          trainingCategories.teacherTitles = res.data.data.teacherTitles || [];
        }
      } catch (error) {
        console.error('获取培训类分类数据失败:', error);
      }
    };

    // 监听项目选择变化，初始化对应的表格
    watch(() => formData.algorithmCategory, (newVal) => {
      if (newVal === '培训类') {
        initTrainingTableRows();
        fetchTrainingCategories();
      } else if (newVal === '工程咨询类') {
        // 初始化工程咨询类表格
      } else {
        // 初始化京东慧采表格
        if (jdTableRows.value.length === 0) {
          addJdTableRow();
        }
        fetchJdCategories();
      }
    });

    onMounted(() => {
      // 根据当前算法类型初始化表格
      if (formData.algorithmCategory === '培训类') {
        initTrainingTableRows();
        fetchTrainingCategories();
      } else if (formData.algorithmCategory === '工程咨询类') {
        // 初始化工程咨询类表格
      } else {
        addJdTableRow();
        fetchJdCategories();
      }
    });

    return {
      activeStep,
      formData,
      isDetailMode, // 导出详情模式标志
      isReportGenerated, // 导出报告生成状态
      selectCalculationMethod,
      getCalculationMethodName,
      nextStep,
      prevStep,
      goBack,
      finishProcess,
      handleReportGenerated // 导出报告生成处理方法
    };
  }
}
</script>

<style lang="scss" scoped>
.project-assistant-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.assistant-header {
  margin-bottom: 30px;
}

.assistant-header h2 {
  font-size: 24px;
  color: #303133;
  margin-bottom: 8px;
}

.subtitle {
  color: #606266;
  font-size: 14px;
}

.steps-container {
  margin-bottom: 30px;
}

.step-content-container {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.step-content {
  padding: 10px;
}

.step-content h3 {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.step-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 25px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #606266;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.half-width {
  flex: 1;
}

.full-width {
  width: 100%;
}

/* 标准表样式 */
.table-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 15px;
  padding-left: 10px;
  border-left: 3px solid #409EFF;
}

.standard-table-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.table-selector {
  padding: 15px;
}

.standard-table, .engineering-table {
  width: 100%;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 10px 0;
  font-weight: 500;
  color: #606266;
  border-bottom: 1px solid #ebeef5;
}

.header-item {
  flex: 1;
  text-align: center;
  padding: 0 5px;
}

.table-row {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.row-item {
  flex: 1;
  padding: 0 5px;
}

.add-row {
  padding: 10px 0;
  text-align: center;
}

.full-width {
  width: 100%;
}

/* 工程咨询类标准表样式 */
.engineering-table .table-header,
.engineering-table .table-row {
  display: grid;
  grid-template-columns: repeat(11, 1fr);
  gap: 5px;
}

.engineering-table .header-item,
.engineering-table .row-item {
  padding: 5px;
  text-align: center;
  font-size: 12px;
}

/* 计算方式选择样式 */
.calculation-methods-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.calculation-method-row {
  display: flex;
  gap: 20px;
}

.calculation-method-card {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
}

.calculation-method-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.calculation-method-card.selected {
  border: 2px solid #409eff;
  background-color: #ecf5ff;
}

.method-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.method-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409eff;
}

.method-header h4 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  color: #303133;
}

.method-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 15px;
  line-height: 1.4;
}

.method-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.feature-item .el-icon {
  color: #67c23a;
  margin-right: 5px;
}

.button-container {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.button-container .el-button {
  min-width: 100px;
}

.standard-table {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  padding: 10px;
  font-weight: bold;
}

.table-row {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ebeef5;
}

.header-item, .row-item {
  flex: 1;
  padding: 0 5px;
}

.full-width {
  width: 100%;
}

.add-row {
  padding: 10px;
  text-align: center;
  border-top: 1px solid #ebeef5;
}

/* 培训类标准表样式 */
.standard-table {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 20px;
  
  .table-header {
    display: flex;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    
    .header-item {
      flex: 1;
      padding: 12px 8px;
      text-align: center;
      font-weight: bold;
      color: #606266;
    }
  }
  
  .table-row {
    display: flex;
    border-bottom: 1px solid #ebeef5;
    
    &:last-child {
      border-bottom: none;
    }
    
    .row-item {
      flex: 1;
      padding: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .add-row {
    padding: 10px;
    text-align: center;
    border-top: 1px solid #ebeef5;
  }
}
</style>





