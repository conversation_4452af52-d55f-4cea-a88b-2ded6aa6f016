# 算法类型响应式更新问题修复说明

## 问题描述
在 `ProjectSelectStep.vue` 组件中，`console.info(props.formData)` 能看到 `algorithmCategory` 有值，但在条件判断 `if (props.formData.algorithmCategory === '培训类')` 时，`props.formData.algorithmCategory` 为空。

## 问题原因
这是由于Vue 3的响应式更新机制导致的时序问题：

1. **异步响应式更新**：Vue 3的响应式更新是异步的，当多个属性同时赋值时，可能存在更新顺序问题
2. **console.info的异步特性**：`console.info` 是异步输出的，所以能看到最终的完整对象
3. **条件判断时机**：在条件判断执行时，响应式更新可能还未完成

## 解决方案
使用 `nextTick` 确保在Vue完成响应式更新后再进行条件判断：

### 修改内容

1. **导入 nextTick**
```javascript
import { ref, reactive, onMounted, watch, nextTick } from 'vue';
```

2. **修复 handleProjectChange 函数**
```javascript
// 使用 nextTick 确保算法类型变化的监听器执行完毕后再解析培训信息
nextTick(() => {
  if (props.formData.algorithmCategory === '培训类' && project.content) {
    // 培训信息解析逻辑...
  }
});
```

3. **修复 nextStep 函数**
```javascript
const nextStep = () => {
  // 验证是否选择了项目
  if (!props.formData.selectedProject) {
    ElMessage.warning('请先选择一个项目');
    return;
  }
  
  // 使用 nextTick 确保响应式更新完成后再进行验证
  nextTick(() => {
    console.log('验证时的算法类型:', props.formData.algorithmCategory);
    
    // 验证逻辑...
    if (props.formData.algorithmCategory === '培训类') {
      // 培训类验证逻辑
    }
    // ...其他验证逻辑
    
    emit('next-step');
  });
};
```

4. **修复 onMounted 函数**
```javascript
onMounted(() => {
  console.info(props.formData);
  loadProjects();
  
  // 使用 nextTick 确保响应式数据完全初始化后再进行条件判断
  nextTick(() => {
    console.log('onMounted中的算法类型:', props.formData.algorithmCategory);
    
    // 根据当前算法类型初始化表格
    if (props.formData.algorithmCategory === '培训类') {
      // 培训类初始化逻辑
    }
    // ...其他初始化逻辑
  });
});
```

## 调试信息
添加了以下调试日志来帮助排查问题：
- `console.log('项目选择后的算法类型:', props.formData.algorithmCategory);`
- `console.log('验证时的算法类型:', props.formData.algorithmCategory);`
- `console.log('onMounted中的算法类型:', props.formData.algorithmCategory);`

## 验证方法
1. 选择一个算法类型为"培训类"的项目
2. 查看浏览器控制台的调试信息
3. 确认条件判断能正确执行
4. 验证培训类标准表能正常显示和操作

## 注意事项
- 这个修复确保了所有涉及 `algorithmCategory` 的条件判断都在Vue响应式更新完成后执行
- 模板中的 `v-if` 和 `v-else-if` 指令不需要修改，因为Vue的模板响应式机制会自动处理
- 如果问题仍然存在，可能需要检查父组件中 `formData` 的初始化和传递方式
